TABLE OF CONTENTS

Chapter I THE PROBLEM AND ITS BACKGROUND
Introduction ........................................................................ 1
Statement of the Problem .......................................... 1
Project Context .......................................................... 2
Purpose and Description ........................................... 2
Objectives of the Study ..................................................... 3
General Objective ...................................................... 3
Specific Objectives .................................................... 3
Scope and Limitations ………............................................. 8
Scope ......................................................................... 8
Limitations .................................................................. 10
Chapter II REVIEW OF RELATED LITERATURE
Review of Related Literature .............................................. 14
Foreign Literature ....................................................... 15
Foreign Studies .......................................................... 16
Local Literature ........................................................... 19
Local Studies .............................................................. 20
Chapter III RESEARCH METHODOLOGY
Research Methodology ..................................................... 23
Respondents ............................................................. 24
Research Instrument ................................................. 25
Data Gathering Procedure ........................................ 26
Statistical Treatment of Data ..................................... 27
Technical Requirements ................................................... 29
Hardware Requirements ........................................... 29
Software Requirements ............................................ 31
System Architecture ......................................................... 40
Data Flow Diagram ................................................... 42
Proposed Flow Chart ................................................. 43
CHAPTER IV PRESENTATION, ANALYSIS AND INTERPRETATION OF DATA
Presentation, Analysis, and Interpretation of Data .......... 47
Demographic Information ........................................... 48
Awareness and Interaction with AI-Generated Text .... 51
Perceived Benefits of AI-Generated Text ................... 53
Perceived Drawbacks of AI-Generated Text .............. 54
Chapter V SUMMARY, CONCLUSION AND RECOMMENDATION
Summary, Conclusion, and Recommendation …............ 71
Summary of Findings ................................................ 71
Conclusion ................................................................ 72
Recommendation ...................................................... 72
Figures ................................................................................ 74
Figure 1.0 Input Process Output Template ................ 4
Figure 13.0 System Architecture ............................... 40
Figure 14.0 Context Diagram ..................................... 41
Figure 15.0 Data Flow Diagram .................................. 42
Figure 17.0 Demographic Charts ................................ 48
Appendices ........................................................................... 76
Appendix A: Survey Questionnaire .............................. 77
Appendix B: Technical Requirements Specification ….. 78
Appendix C: Sample Results from AI Detection …...... 79



