TABLE OF CONTENTS

ABSTRACT ......................................................................... i
ACKNOWLEDGMENTS .................................................... ii
DEDICATION .................................................................... iii
PURPOSE AND DESCRIPTION ........................................ iv

CHAPTER I: THE PROBLEM AND ITS BACKGROUND
Introduction ........................................................................ 1
Statement of the Problem .................................................. 2
    Problem Statement .................................................... 2
    Research Questions .................................................. 3
Project Context .................................................................. 4
    Background of the Study ........................................... 4
    Significance of the Problem ...................................... 5
Purpose and Description .................................................... 6
    Research Purpose ..................................................... 6
    System Description ................................................... 7
Objectives of the Study ..................................................... 8
    General Objective ...................................................... 8
    Specific Objectives .................................................... 9
Scope and Limitations ....................................................... 11
    Scope of the Study ................................................... 11
    Limitations of the Study ............................................ 13
Significance of the Study .................................................. 15
    Theoretical Significance ........................................... 15
    Practical Significance ............................................... 16

CHAPTER II: REVIEW OF RELATED LITERATURE
Review of Related Literature ............................................ 18
    Conceptual Framework ............................................. 18
    Theoretical Foundation ............................................. 19
Foreign Literature ............................................................. 21
    AI Text Generation Technologies ............................. 21
    Detection Methodologies .......................................... 23
    Machine Learning Approaches ................................. 25
Foreign Studies ................................................................ 27
    Comparative Analysis Studies .................................. 27
    Performance Evaluation Studies .............................. 29
    User Behavior Studies ............................................. 31
Local Literature ................................................................ 33
    Philippine Context Studies ....................................... 33
    Educational Technology Research ........................... 35
Local Studies ................................................................... 37
    Academic Integrity Studies ...................................... 37
    Digital Literacy Research ......................................... 39
Synthesis of Related Literature ........................................ 41
    Research Gaps Identified ......................................... 41
    Theoretical Framework Development ....................... 43

CHAPTER III: RESEARCH METHODOLOGY
Research Design and Approach ....................................... 45
    Research Paradigm .................................................. 45
    Methodological Framework ...................................... 46
Research Methodology ..................................................... 48
    Data Collection Methods .......................................... 48
    Analysis Procedures ................................................ 49
Respondents and Sampling .............................................. 51
    Target Population .................................................... 51
    Sampling Technique ................................................ 52
    Sample Size Determination ..................................... 53
Research Instruments ...................................................... 55
    Survey Questionnaire Design .................................. 55
    System Evaluation Tools ......................................... 56
    Validation Procedures ............................................. 57
Data Gathering Procedures .............................................. 59
    Primary Data Collection ........................................... 59
    Secondary Data Sources ......................................... 60
    Ethical Considerations ............................................. 61
Statistical Treatment of Data ............................................ 63
    Descriptive Statistics ............................................... 63
    Inferential Statistics ................................................. 64
    Data Analysis Software ........................................... 65
System Development Methodology .................................. 67
    Development Framework ......................................... 67
    Algorithm Implementation ........................................ 68
    Testing Procedures ................................................. 69
Technical Requirements ................................................... 71
    Hardware Requirements .......................................... 71
    Software Requirements ........................................... 73
    Network Requirements ............................................ 75
System Architecture ......................................................... 77
    System Design Overview ......................................... 77
    Component Architecture .......................................... 78
    Database Design ..................................................... 79
Data Flow Diagram .......................................................... 81
    Level 0 DFD ............................................................ 81
    Level 1 DFD ............................................................ 82
Proposed System Flow Chart ........................................... 84
    User Interface Flow ................................................. 84
    Processing Flow ...................................................... 85
About Us Page ................................................................. 87
    Project Team Information ........................................ 87
    System Objectives ................................................... 88

CHAPTER IV: PRESENTATION, ANALYSIS AND INTERPRETATION OF DATA
Data Presentation Overview ............................................ 90
    Data Collection Summary ........................................ 90
    Analysis Framework ................................................ 91
Demographic Profile of Respondents ............................... 93
    Participant Characteristics ...................................... 93
    Demographic Distribution ........................................ 95
    Sample Representativeness .................................... 97
System Performance Analysis .......................................... 99
    Algorithm Accuracy Results .................................... 99
    Processing Speed Evaluation ................................. 101
    Comparative Performance Analysis ........................ 103
User Awareness and Interaction Analysis ....................... 105
    AI-Generated Text Recognition .............................. 105
    Platform Usage Patterns ....................................... 107
    Detection Tool Acceptance .................................... 109
Perceived Benefits of AI-Generated Text ........................ 111
    Positive Applications Identified ............................... 111
    User Appreciation Levels ....................................... 113
    Educational Benefits Assessment .......................... 115
Perceived Drawbacks and Risks .................................... 117
    Misinformation Concerns ....................................... 117
    Academic Integrity Issues ...................................... 119
    Trust and Credibility Factors ................................. 121
System Usability Evaluation ........................................... 123
    User Interface Assessment .................................... 123
    Functionality Testing Results ................................. 125
    User Satisfaction Metrics ....................................... 127
Statistical Analysis Results ............................................. 129
    Hypothesis Testing ................................................ 129
    Correlation Analysis ............................................... 131
    Regression Analysis .............................................. 133
Discussion of Findings .................................................... 135
    Key Research Insights ........................................... 135
    Implications for Practice ........................................ 137
    Theoretical Contributions ....................................... 139

CHAPTER V: SUMMARY, CONCLUSIONS AND RECOMMENDATIONS
Summary of the Study .................................................... 141
    Research Overview ................................................ 141
    Methodology Summary ........................................... 142
Summary of Findings ...................................................... 144
    Primary Research Results ...................................... 144
    Secondary Findings ............................................... 146
    Statistical Outcomes .............................................. 148
Conclusions ..................................................................... 150
    Research Question Answers .................................. 150
    Hypothesis Validation ............................................ 152
    Theoretical Implications ......................................... 154
Recommendations .......................................................... 156
    For System Enhancement ...................................... 156
    For Future Research ............................................. 158
    For Educational Implementation ............................. 160
    For Policy Development ......................................... 162
Limitations and Future Work ........................................... 164
    Study Limitations ................................................... 164
    Future Research Directions ................................... 166

REFERENCES ................................................................. 168

FIGURES
Figure 1.0 Input Process Output Template ......................... 4
Figure 2.0 Conceptual Framework ................................... 20
Figure 3.0 Theoretical Model ........................................... 44
Figure 4.0 Research Design Framework .......................... 47
Figure 5.0 Sampling Procedure ....................................... 54
Figure 6.0 Data Collection Process .................................. 62
Figure 7.0 System Development Lifecycle ....................... 70
Figure 8.0 Hardware Architecture .................................... 72
Figure 9.0 Software Components .................................... 74
Figure 10.0 Network Topology ......................................... 76
Figure 11.0 System Architecture Overview ....................... 78
Figure 12.0 Database Schema ......................................... 80
Figure 13.0 Level 0 Data Flow Diagram ........................... 82
Figure 14.0 Level 1 Data Flow Diagram ........................... 83
Figure 15.0 System Flow Chart ........................................ 86
Figure 16.0 About Us Page Layout ................................... 89
Figure 17.0 Demographic Distribution Charts ................... 96
Figure 18.0 Performance Comparison Graph ................. 104
Figure 19.0 User Awareness Statistics ........................... 108
Figure 20.0 Benefits Assessment Chart ......................... 114
Figure 21.0 Risk Perception Analysis ............................. 120
Figure 22.0 Usability Evaluation Results ........................ 126
Figure 23.0 Statistical Analysis Summary ....................... 134

TABLES
Table 1.0 Research Questions and Objectives .................. 10
Table 2.0 Literature Review Summary .............................. 42
Table 3.0 Sample Size Calculation ................................... 53
Table 4.0 Research Instruments Overview ....................... 58
Table 5.0 Data Collection Timeline ................................... 61
Table 6.0 Statistical Methods Applied ............................... 66
Table 7.0 Hardware Specifications .................................. 72
Table 8.0 Software Requirements .................................... 74
Table 9.0 System Components Description ...................... 79
Table 10.0 Demographic Profile Summary ........................ 94
Table 11.0 Algorithm Performance Metrics ..................... 100
Table 12.0 User Interaction Analysis ............................... 106
Table 13.0 Benefits Ranking Results .............................. 112
Table 14.0 Risk Assessment Findings ............................ 118
Table 15.0 Usability Testing Results ............................... 124
Table 16.0 Statistical Test Results .................................. 130
Table 17.0 Correlation Matrix .......................................... 132
Table 18.0 Research Findings Summary ......................... 145

APPENDICES
Appendix A: Survey Questionnaire ................................. 170
Appendix B: Technical Requirements Specification ........ 175
Appendix C: Sample AI Detection Results ...................... 180
Appendix D: System Source Code .................................. 185
Appendix E: User Interface Screenshots ......................... 190
Appendix F: Statistical Analysis Output .......................... 195
Appendix G: Ethical Clearance Documentation .............. 200
Appendix H: Expert Validation Forms ............................. 205



